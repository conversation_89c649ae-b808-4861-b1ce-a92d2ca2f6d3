package handlers

import (
	"fmt"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"
	"strconv"
	"strings"

	"github.com/gofiber/fiber/v2"
)

type OperationHandler struct {
	operationService ports.OperationService
}

func NewOperationHandler(operationService ports.OperationService) *OperationHandler {
	return &OperationHandler{
		operationService: operationService,
	}
}

func (h *OperationHandler) GetOperationData(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid Cluster ID parameter")
	}

	// Get optional namespace_id query parameter
	var namespaceId uint64 = 0
	namespaceIdStr := c.Query("namespace_id")
	if namespaceIdStr != "" {
		namespaceId, err = strconv.ParseUint(namespaceIdStr, 10, 64)
		if err != nil {
			return response.Error(c, fiber.StatusBadRequest, "Invalid Namespace ID parameter")
		}
	}

	operationData, err := h.operationService.GetOperationData(id, namespaceId)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Operation data retrieved successfully", operationData)
}

func (h *OperationHandler) CreateOperation(c *fiber.Ctx) error {
	var req dto.OperationCreateReq
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request body")
	}

	// Get user ID from authentication context
	userID := c.Locals("user_id")
	if userID == nil {
		return response.Error(c, fiber.StatusUnauthorized, "User authentication required")
	}

	userIDUint64, ok := userID.(uint64)
	if !ok {
		return response.Error(c, fiber.StatusUnauthorized, "Invalid user ID format")
	}

	accessToken := c.Get("Authorization")
	if accessToken == "" {
		return response.Error(c, fiber.StatusUnauthorized, "Authorization header is required")
	}

	// Remove "Bearer " prefix to get just the token
	if strings.HasPrefix(accessToken, "Bearer ") {
		accessToken = strings.TrimPrefix(accessToken, "Bearer ")
	}

	fmt.Printf("AUTHORIZATION: %s", accessToken)

	result, err := h.operationService.CreateOperationAsync(userIDUint64, accessToken, req)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Operation created successfully", result)
}

func (h *OperationHandler) CreateCluster(c *fiber.Ctx) error {
	var req dto.OperationCreateReq
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request body")
	}

	// Get user ID from authentication context
	userID := c.Locals("user_id")
	if userID == nil {
		return response.Error(c, fiber.StatusUnauthorized, "User authentication required")
	}

	userIDUint64, ok := userID.(uint64)
	if !ok {
		return response.Error(c, fiber.StatusUnauthorized, "Invalid user ID format")
	}

	accessToken := c.Get("Authorization")
	if accessToken == "" {
		return response.Error(c, fiber.StatusUnauthorized, "Authorization header is required")
	}

	// Remove "Bearer " prefix to get just the token
	if strings.HasPrefix(accessToken, "Bearer ") {
		accessToken = strings.TrimPrefix(accessToken, "Bearer ")
	}

	result, err := h.operationService.CreateClusterAsync(userIDUint64, accessToken, req)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Cluster created successfully", result)
}
